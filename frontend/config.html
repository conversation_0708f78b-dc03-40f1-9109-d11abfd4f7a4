<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration - JMSC Virtual Seller</title>
    <link rel="stylesheet" href="/static/css/config.css">
</head>
<body>
    <div class="container">
        <h1>Virtual Seller Configuration</h1>
        <form id="config-form">
            <div class="section" id="category-strategies-section">
                <h2>Category-Specific Strategies</h2>
                <p>Select up to 5 categories to configure their selling strategy.</p>
                <div class="category-selection-container">
                    <label for="category-selector">Select Categories:</label>
                    <select id="category-selector" multiple size="5">
                        <option value="Vodka">Vodka</option>
                        <option value="Beer">Beer</option>
                        <option value="Whiskey">Whiskey</option>
                        <option value="Wine">Wine</option>
                        <option value="Gin">Gin</option>
                        <option value="Rum">Rum</option>
                        <option value="Tequila">Tequila</n>
                        <option value="Brandy">Brandy</option>
                        <option value="Liqueurs">Liqueurs</option>
                        <option value="Sake">Sake</option>
                    </select>
                </div>
                <div id="selected-categories-container">
                    <!-- Dynamically added category configuration rows will go here -->
                </div>
            </div>

            <div class="section">
                <h2>Consumer Experience</h2>
                <p>Customize the customer-facing interaction style.</p>
                <label for="chat-persona">Chat Persona:</label>
                <select id="chat-persona" name="chat_persona">
                    <option value="friendly_bartender">Friendly Bartender</option>
                    <option value="formal_sommelier">Formal Sommelier</option>
                    <option value="quick_assistant">Quick & Efficient Assistant</option>
                </select>

                <label for="welcome-message">Custom Welcome Message:</label>
                <textarea id="welcome-message" name="welcome_message" rows="3"></textarea>
            </div>

            <div class="section">
                <h2>Store Branding</h2>
                <p>Personalize the look and feel of your virtual store.</p>
                <label for="store-name">Store Name:</label>
                <input type="text" id="store-name" name="store_name">

                <label for="theme">Color Theme:</label>
                <select id="theme" name="theme">
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                </select>
            </div>

            <button type="submit">Save Configuration</button>
        </form>
    </div>
    <script src="/js/config.js"></script>
</body>
</html>
