document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('config-form');
    const categorySelector = document.getElementById('category-selector');
    const selectedCategoriesContainer = document.getElementById('selected-categories-container');
    const MAX_CATEGORIES = 5;

    const createCategoryConfigRow = (category, strategy = 'balanced') => {
        const div = document.createElement('div');
        div.classList.add('category-strategy-item');
        div.dataset.category = category;
        div.innerHTML = `
            <span>${category}</span>
            <select class="category-strategy-select">
                <option value="high_margin">High-Margin</option>
                <option value="high_inventory">High-Inventory</option>
                <option value="balanced">Balanced</option>
            </select>
        `;
        div.querySelector('.category-strategy-select').value = strategy;
        selectedCategoriesContainer.appendChild(div);
    };

    categorySelector.addEventListener('change', () => {
        const selectedOptions = Array.from(categorySelector.selectedOptions);

        if (selectedOptions.length > MAX_CATEGORIES) {
            alert(`You can select a maximum of ${MAX_CATEGORIES} categories.`);
            // Deselect the last selected option to enforce the limit
            selectedOptions[selectedOptions.length - 1].selected = false;
            return;
        }

        // Clear existing configured categories
        selectedCategoriesContainer.innerHTML = '';

        // Add new configured categories based on selection
        selectedOptions.forEach(option => {
            createCategoryConfigRow(option.value);
        });
    });

    // Fetch existing configuration and populate the form
    fetch('/api/config')
        .then(response => response.json())
        .then(config => {
            document.getElementById('chat-persona').value = config.chat_persona;
            document.getElementById('welcome-message').value = config.welcome_message;
            document.getElementById('store-name').value = config.store_name;
            document.getElementById('theme').value = config.theme;

            // Populate category strategies
            if (config.category_strategies) {
                const currentCategories = config.category_strategies.map(cat => cat.category);
                
                // Select categories in the dropdown
                Array.from(categorySelector.options).forEach(option => {
                    if (currentCategories.includes(option.value)) {
                        option.selected = true;
                    }
                });

                // Create config rows for pre-selected categories
                config.category_strategies.forEach(cat => {
                    createCategoryConfigRow(cat.category, cat.strategy);
                });
            }
        });

    // Handle form submission
    form.addEventListener('submit', (event) => {
        event.preventDefault();

        const category_strategies = [];
        document.querySelectorAll('.category-strategy-item').forEach(row => {
            const category = row.dataset.category;
            const strategy = row.querySelector('.category-strategy-select').value;
            category_strategies.push({ category, strategy });
        });

        const config = {
            chat_persona: document.getElementById('chat-persona').value,
            welcome_message: document.getElementById('welcome-message').value,
            store_name: document.getElementById('store-name').value,
            theme: document.getElementById('theme').value,
            category_strategies: category_strategies
        };

        fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(config),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Configuration saved successfully!');
            } else {
                alert('Error saving configuration.');
            }
        });
    });
});
