document.addEventListener('DOMContentLoaded', function() {
    const categoryCheckboxesContainer = document.getElementById('category-checkboxes');
    const selectedCategoriesContainer = document.getElementById('selected-categories-container');
    const recommendationPreferenceSelect = document.getElementById('recommendationPreference');
    const agentConfigForm = document.getElementById('config-form');
    const statusMessageDiv = document.getElementById('statusMessage');
    const categorySearchInput = document.getElementById('categorySearch');
    const themeSelect = document.getElementById('theme'); // Get the theme select element
    let allCategories = [];
    let selectedCategories = new Map(); // Use a Map to store selected categories and their strategies

    const CATEGORY_STRATEGIES = [
        "High_margin",
        "Stock_wise",
        "New_arrivals",
        "Best_sellers",
        "Seasonal_focus"
    ];

    // Function to display status messages
    function showStatusMessage(message, type) {
        statusMessageDiv.textContent = message;
        statusMessageDiv.className = `status-message ${type}`;
        statusMessageDiv.style.display = 'block';
        setTimeout(() => {
            statusMessageDiv.style.display = 'none';
        }, 3000);
    }

    // Function to render categories as checkboxes
    function renderCategoryCheckboxes(categoriesToDisplay) {
        categoryCheckboxesContainer.innerHTML = ''; // Clear existing checkboxes
        categoriesToDisplay.forEach(category => {
            const checkboxId = `category-${category.replace(/\s/g, '-')}`;
            const checkboxDiv = document.createElement('div');
            checkboxDiv.className = 'category-checkbox-item';
            checkboxDiv.innerHTML = `
                <input type="checkbox" id="${checkboxId}" value="${category}" ${selectedCategories.has(category) ? 'checked' : ''}>
                <label for="${checkboxId}">${category}</label>
            `;
            categoryCheckboxesContainer.appendChild(checkboxDiv);

            // Add event listener for each checkbox
            checkboxDiv.querySelector(`#${checkboxId}`).addEventListener('change', function() {
                if (this.checked) {
                    if (selectedCategories.size < 5) {
                        selectedCategories.set(this.value, CATEGORY_STRATEGIES[0]); // Add with default strategy
                    } else {
                        this.checked = false; // Prevent selection if limit reached
                        alert('You can select a maximum of 5 categories. To select a new one, please uncheck an already selected category first.');
                    }
                } else {
                    selectedCategories.delete(this.value);
                }
                renderSelectedCategories(); // Re-render the list of selected categories
            });
        });
    }

    // Function to render selected categories in the container
    function renderSelectedCategories() {
        selectedCategoriesContainer.innerHTML = ''; // Clear existing
        selectedCategories.forEach((strategy, category) => {
            const categoryRow = document.createElement('div');
            categoryRow.className = 'category-config-row';
            categoryRow.dataset.category = category; // Store category name for easy access

            const strategyOptions = CATEGORY_STRATEGIES.map(s => 
                `<option value="${s}" ${s === strategy ? 'selected' : ''}>${s.replace(/_/g, ' ')}</option>`
            ).join('');

            categoryRow.innerHTML = `
                <span>${category}</span>
                <select class="category-strategy-select" data-category="${category}">
                    ${strategyOptions}
                </select>
                <button type="button" class="remove-category-btn" data-category="${category}">
                    <i class="fas fa-times"></i>
                </button>
            `;
            selectedCategoriesContainer.appendChild(categoryRow);
        });

        // Add event listeners to new remove buttons
        selectedCategoriesContainer.querySelectorAll('.remove-category-btn').forEach(button => {
            button.addEventListener('click', function() {
                const categoryToRemove = this.dataset.category;
                selectedCategories.delete(categoryToRemove);
                renderSelectedCategories(); // Re-render the list

                // Also uncheck the corresponding checkbox
                const checkboxToUncheck = document.getElementById(`category-${categoryToRemove.replace(/\s/g, '-')}`);
                if (checkboxToUncheck) {
                    checkboxToUncheck.checked = false;
                }
            });
        });

        // Add event listeners for strategy select dropdowns
        selectedCategoriesContainer.querySelectorAll('.category-strategy-select').forEach(select => {
            select.addEventListener('change', function() {
                const category = this.dataset.category;
                const newStrategy = this.value;
                selectedCategories.set(category, newStrategy);
                console.log(`Updated strategy for ${category} to ${newStrategy}`);
            });
        });
    }

    // Fetch categories and populate the checkboxes
    async function fetchCategories() {
        try {
            const response = await fetch('http://localhost:8000/admin_api/categories');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            allCategories = data.categories.sort(); // Sort categories alphabetically (A to Z)
            renderCategoryCheckboxes(allCategories);
        } catch (error) {
            console.error('Error fetching categories:', error);
            showStatusMessage('Failed to load categories.', 'error');
        }
    }

    // Filter categories based on search input
    categorySearchInput.addEventListener('input', function() {
        const searchTerm = categorySearchInput.value.toLowerCase();
        const filteredCategories = allCategories.filter(category =>
            category.toLowerCase().includes(searchTerm)
        );
        renderCategoryCheckboxes(filteredCategories);
    });

    // Load current configuration
    async function loadConfig() {
        try {
            const response = await fetch('http://localhost:8000/admin_api/config');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const config = await response.json();

            // Load selected categories and their strategies
            if (config.selected_categories && Array.isArray(config.selected_categories)) {
                selectedCategories.clear(); // Clear existing map
                config.selected_categories.slice(0, 5).forEach(item => { // Ensure max 5
                    // Assuming item is { category: "Name", strategy: "Strategy" }
                    // If it's just a string, default to first strategy
                    if (typeof item === 'string') {
                        selectedCategories.set(item, CATEGORY_STRATEGIES[0]);
                    } else if (item.category && item.strategy) {
                        selectedCategories.set(item.category, item.strategy);
                    }
                });
                renderSelectedCategories();
                // Re-render checkboxes to reflect loaded selections
                renderCategoryCheckboxes(allCategories);
            }

            recommendationPreferenceSelect.value = config.recommendation_preference || 'all_products';
            document.getElementById('chat-persona').value = config.chat_persona || 'friendly_bartender';
            document.getElementById('welcome-message').value = config.welcome_message || '';
            document.getElementById('store-name').value = config.store_name || '';
            themeSelect.value = config.theme || 'light';
            applyTheme(themeSelect.value); // Apply theme on load

        } catch (error) {
            console.error('Error loading configuration:', error);
            showStatusMessage('Failed to load current configuration.', 'error');
        }
    }

    // Function to apply the selected theme
    function applyTheme(theme) {
        if (theme === 'dark') {
            document.body.classList.add('dark-theme');
        } else {
            document.body.classList.remove('dark-theme');
        }
    }

    // Add event listener for theme selection change
    themeSelect.addEventListener('change', function() {
        applyTheme(this.value);
    });

    // Handle form submission to save configuration
    agentConfigForm.addEventListener('submit', async function(event) {
        event.preventDefault();

        // Convert Map to an array of objects for saving
        const categoriesToSave = Array.from(selectedCategories).map(([category, strategy]) => ({
            category: category,
            strategy: strategy
        }));

        const config = {
            selected_categories: categoriesToSave,
            recommendation_preference: recommendationPreferenceSelect.value,
            chat_persona: document.getElementById('chat-persona').value,
            welcome_message: document.getElementById('welcome-message').value,
            store_name: document.getElementById('store-name').value,
            theme: document.getElementById('theme').value
        };

        try {
            const response = await fetch('http://localhost:8000/admin_api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            showStatusMessage(result.message, 'success');
            console.log('Configuration saved:', result.config);
        } catch (error) {
            console.error('Error saving configuration:', error);
            showStatusMessage('Failed to save configuration.', 'error');
        }
    });

    // Initial load
    fetchCategories();
    loadConfig();
});
