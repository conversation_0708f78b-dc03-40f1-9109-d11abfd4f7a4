<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover"
    />
    <title>Audio Visualizer UI</title>
    <link rel="stylesheet" href="./css/index.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
  </head>
  <body>
    <!-- Landing Page -->
    <div class="landing-container" id="landingContainer">
      <div class="landing-content">
        <h1 class="landing-title">Meet your AI-powered shopping assistant</h1>
        <p class="landing-subtitle">
          Ask for recommendations. Get smarter choices instantly.
        </p>
        <button
          id="startChatBtn"
          class="start-chat-btn"
          onclick="toggleFullscreen()"
        >
          Start Chatting
        </button>
      </div>
      <div class="landing-footer"><p>Powered by </p><img class="footerLogo" src="https://upc-pull-dev.jmscpos.online/assets/logo/JMSCPOS-Logo-New.png"></div>
      <!-- JMSC -->
    </div>

    <!-- Main App Layout (Initially Hidden) -->
    <div class="main-layout" id="mainLayout" style="display: none">
      <!-- New Header -->
      <header class="app-header">
        <div class="logo"><span class="highlight">AI</span> Assistance</div>
        <button
          id="fullscreenBtn"
          class="fullscreen-btn"
          title="Toggle Fullscreen"
        >
          <i class="fas fa-expand" id="fullscreenIcon"></i>
        </button>
      </header>

      <div class="content-container">
        <div class="left-panel">
          <div class="visualizer-container">
            <div class="main-circle">
              <div class="outer-ring"></div>
              <div class="audio-bars">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
              </div>
            </div>
            <div
              class="listening-indicator"
              id="listeningIndicator"
              style="display: none"
            >
              Listening...
              <div class="listening-icon"></div>
            </div>
          </div>
          <div class="message-container">
            <div id="messages"></div>
            <form id="messageForm">
              <textarea
                id="message"
                placeholder="Ask me anything about products..."
                rows="1"
              ></textarea>
              <button type="button" id="micButton" class="mic-button">
                <i class="fas fa-microphone"></i>
              </button>
              <button type="submit" id="sendButton">
                <i class="fas fa-paper-plane"></i>
              </button>
            </form>
            <div
              class="loading-indicator"
              id="loadingIndicator"
              style="display: none"
            >
              <div class="loading-square"></div>
            </div>
          </div>
        </div>
        <div class="right-panel" id="rightPanel">
          <div class="recommendations-panel">
            <h2>Product Gallery</h2>
            <div id="recommendedProducts" class="product-grid">
              <!-- Products will be displayed here in a grid -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="/js/app.js" type="module"></script>
  </body>
</html>
