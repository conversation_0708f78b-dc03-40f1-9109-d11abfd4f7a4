from typing import AsyncGenerator
from google.adk.agents import Agent
from google.adk.agents.invocation_context import Invocation<PERSON>ontext
from google.adk.events import Event
from google.genai.types import Content, Part, FunctionCall
from google.adk.tools import google_search


class RecipeAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        # This agent is called when a recipe is requested.
        # It should use the google_search tool to find a recipe.
        # Then, it should format the recipe into the specified JSON structure.

        # 1. Construct a search query for the recipe.
        user_query = ""
        if ctx.user_content and ctx.user_content.parts:
            user_query = ctx.user_content.parts[0].text
        
        if not user_query:
            # Handle case where user query is empty
            yield Event(
                author=self.name,
                content=Content(parts=[Part.from_text(text="I'm sorry, I didn't get that. What recipe are you looking for?")])
            )
            return
        search_query = f"recipe for {user_query}"

        # 2. Call the google_search tool.
        yield Event(
            author=self.name,
            content=Content(
                parts=[
                    Part.from_function_call(
                        FunctionCall(
                            name=google_search.name,
                            args={"query": search_query}
                        )
                    )
                ]
            )
        )


recipe_agent = RecipeAgent(
    name="recipe_agent",
    model="gemini-2.0-flash-live-001",
    description="Agent that finds recipes using Google Search and formats them for the user.",
    tools=[google_search],
)