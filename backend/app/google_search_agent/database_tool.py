import os
import mysql.connector
import requests # New import
import json # New import
from google.adk.tools import ToolContext
from typing import Dict, Any, List # Updated import
from decimal import Decimal # Import Decimal

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def convert_decimals(data):
    """Recursively converts Decimal objects in a list of dictionaries to strings."""
    if isinstance(data, list):
        for item in data:
            if isinstance(item, dict):
                for key, value in item.items():
                    if isinstance(value, Decimal):
                        item[key] = str(value)
                    elif isinstance(value, (dict, list)):
                        item[key] = convert_decimals(value) # Recurse for nested structures
            elif isinstance(item, Decimal):
                item = str(item) # Handle list of Decimals directly
    elif isinstance(data, dict):
        for key, value in data.items():
            if isinstance(value, Decimal):
                data[key] = str(value)
            elif isinstance(value, (dict, list)):
                data[key] = convert_decimals(value) # Recurse for nested structures
    return data

def get_db_connection():
    """Establishes and returns a MySQL database connection."""
    print("************0*******************")
    return mysql.connector.connect(
        host=os.getenv("MYSQL_HOST", "localhost"),
        user=os.getenv("MYSQL_USER", "user"),
        password=os.getenv("MYSQL_PASSWORD", "password"),
        database=os.getenv("MYSQL_DB", "mydatabase")
    )

def execute_sql_query(query: str, tool_context: ToolContext) -> Dict[str, Any]:
    """
    Executes a read-only SQL query against the MySQL database and returns the results.

    Use this tool when the user asks a question that requires querying the database.
    The query must be a SELECT statement. Do NOT use INSERT, UPDATE, DELETE, or DDL statements.

    Args:
        query: The SQL SELECT query to execute.
        tool_context: The context for the tool, providing access to session state.

    Returns:
        A dictionary containing the query results.
        Possible statuses: 'success', 'error'.
        If 'success', includes 'columns' (list of column names) and 'rows' (list of lists of row data).
        If 'error', includes 'error_message'.
    """
    print(f"Tool: Attempting to execute SQL query: {query}")
    conn = None
    cursor = None
    try:
        # Ensure only SELECT queries are executed for safety
        if not query.strip().lower().startswith("select"):
            return {
                "status": "error",
                "error_message": "Only SELECT queries are allowed for security reasons."
            }

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(query)

        # Fetch column names
        columns = [i[0] for i in cursor.description] if cursor.description else []
        
        # Fetch all rows
        rows = cursor.fetchall()
        
        print(f"Tool: Successfully executed query. Rows returned: {len(rows)}")

        # Check if the query returned product data (contains 'item_code')
        if "item_code" in columns:
            upcs = []
            item_code_index = columns.index("item_code")
            for row in rows:
                upcs.append(str(row[item_code_index])) # Ensure UPC is string
                
            print(f"\n *********** \n {upcs}\n ******* \n  ")

            if upcs:
                print(f"[database_tool]: Fetching product info for UPCs: {upcs}")
                product_info_url = "https://backend-upc-pull-dev.jmscpos.online/api/ProductSearch/BatchSearch"
                product_info_headers = {
                    "accept": "*/*",
                    "Content-Type": "application/json"
                }
                try:
                    product_info_response = requests.post(product_info_url, headers=product_info_headers, data=json.dumps(upcs))
                    product_info_response.raise_for_status()
                    product_info_data = product_info_response.json()

                    # Create a map for quick lookup of product details by UPC
                    product_details_map: Dict[str, Dict[str, Any]] = {}
                    if product_info_data and product_info_data.get("products"):
                        for detail in product_info_data["products"]:
                            if detail.get("upc"):
                                product_details_map[str(detail["upc"])] = detail
                    
                    # Merge product details into the database rows
                    merged_rows = []
                    # Add new columns for product info if they don't exist
                    # Change "image_url" to "images" to match frontend expectation
                    new_columns = ["description", "brandName", "images", "productInsight","pairingInfo"]
                    for col in new_columns:
                        if col not in columns:
                            columns.append(col)

                    for row_data in rows:
                        row_list = list(row_data) # Convert tuple to list for modification
                        current_item_code = str(row_data[item_code_index])
                        
                        # Initialize new fields to None for the current row
                        description = None
                        brand_name = None
                        images = [] # Initialize as empty list
                        product_insight = None
                        pairingInfo = None

                        if current_item_code in product_details_map:
                            info_detail = product_details_map[current_item_code]
                            description = info_detail.get("description")
                            brand_name = info_detail.get("brandName")
                            pairingInfo =  info_detail.get("pairingInfo")
                            if info_detail.get("images"):
                                # Store the entire images array if available
                                images = info_detail["images"] 
                            product_insight = info_detail.get("productInsight")
                        
                        # Append the new fields to the row
                        row_list.append(description)
                        row_list.append(brand_name)
                        row_list.append(images) # Append the images list
                        row_list.append(product_insight)
                        row_list.append(pairingInfo)
                        merged_rows.append(row_list)
                    print("merged_rows",merged_rows)
                    
                    # Convert merged_rows into a list of dictionaries
                    products_list = []
                    for row_data in merged_rows:
                        product_dict = {}
                        for i, col_name in enumerate(columns):
                            product_dict[col_name] = row_data[i]
                        products_list.append(product_dict)
                    
                    # Convert Decimal objects to strings before storing
                    products_list = convert_decimals(products_list)

                    enriched_data = {
                            "status": "success", # Always report success to frontend, even if partial
                            "data": {"products": products_list} # Wrap in 'products' key as expected by frontend
                        }
                    tool_context.state["products_data"] = enriched_data # Store in session state
                    print(20*"\n",enriched_data)
                    
                    return enriched_data

                except requests.exceptions.RequestException as e:
                    print(f"Tool: Error fetching product info: {e}")
                    # If product info fetch fails, return original database results
                    # but convert them to a list of dictionaries for consistency
                    products_list = []
                    for row_data in rows:
                        product_dict = {}
                        for i, col_name in enumerate(columns):
                            product_dict[col_name] = row_data[i]
                        products_list.append(product_dict)

                    # Convert Decimal objects to strings before returning
                    products_list = convert_decimals(products_list)
                    return {
                        "status": "success",
                        "data": {
                            "products": products_list,
                            "product_info_error": str(e) # Indicate error but still return db data
                        }
                    }
        
        # If the query was for products (contains 'item_code') or product-like (contains 'item_name'),
        # convert the original database results to a list of dictionaries for consistency.
        if "item_code" in columns or "item_name" in columns:
            products_list = []
            for row_data in rows:
                product_dict = {}
                for i, col_name in enumerate(columns):
                    product_dict[col_name] = row_data[i]
                products_list.append(product_dict)
            
            # Convert Decimal objects to strings before storing
            products_list = convert_decimals(products_list)

            # Store in session state even if no external product info was fetched
            tool_context.state["products_data"] = {
                "status": "success",
                "data": {"products": products_list}
            }
            return {
                "status": "success",
                "products": products_list
            }
        else:
            # For non-product queries (e.g., categories), return columns and rows as before
            # Ensure Decimal values are converted for these as well
            converted_rows = convert_decimals([dict(zip(columns, row)) for row in rows])
            # Do NOT set tool_context.state["products_data"] for non-product queries
            return {
                "status": "success",
                "columns": columns,
                "rows": [list(row.values()) for row in converted_rows] # Convert back to list of lists if needed by caller
            }
    except mysql.connector.Error as err:
        print(f"Tool: Error executing query: {err}")
        return {
            "status": "error",
            "error_message": str(err)
        }
    finally:
        if cursor:
            cursor.close()
        if conn and conn.is_connected():
            conn.close()
