import json
import json
from typing import AsyncGenerator, List, Dict, Any

from google.adk.agents import Agent
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions
from google.genai.types import Content, Part

class RecommendationAgent(Agent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    async def _run_async_impl(self, ctx: InvocationContext) -> AsyncGenerator[Event, None]:
        # This agent will receive the output from the DatabaseAgent in the context
        database_output = ctx.session.state.get("database_output")

        products_to_recommend: List[Dict[str, Any]] = []

        # Check for product data explicitly set by database_tool in session state
        products_data_from_state = ctx.session.state.get("products_data")
        if products_data_from_state and products_data_from_state.get("status") == "success" and products_data_from_state.get("data") and products_data_from_state["data"].get("products"):
            products_to_recommend = products_data_from_state["data"]["products"]
            print(f"[RecommendationAgent]: Products from session state: {len(products_to_recommend)} products.")

            recommendation_json = {
                "type": "recommendations",
                "products": products_to_recommend
            }
            # Generate a conversational response listing the products
            product_names = [p.get("item_name", "unknown product") for p in products_to_recommend[:3]] # List up to 3 products
            response_text = f"Certainly! We have {', '.join(product_names)}"
            if len(products_to_recommend) > 3:
                response_text += f" and more, including {len(products_to_recommend) - 3} other options."
            response_text += ". Take a look at these options:"

            print(f"[RecommendationAgent]: Sending conversational text and JSON to frontend.")
            yield Event(
                author=self.name,
                content=Content(
                    parts=[
                        Part.from_text(text=response_text),
                        Part.from_data(data=json.dumps(recommendation_json).encode('utf-8'), mime_type="application/json")
                    ],
                    mime_type="application/json"
                ),
                actions=EventActions(turn_complete=True)
            )
            return # Exit after yielding product response

        # If no products in session state, check raw database_output for categories or errors
        if database_output and database_output.get("status") == "success":
            columns = database_output.get("columns", [])
            rows = database_output.get("rows", [])
            print(f"[RecommendationAgent]: Handling non-product query. Columns: {columns}, Rows: {len(rows)}")

            if "main_category" in columns: # This is a category query result
                categories = [row[0] for row in rows]
                if categories:
                    category_list = ", ".join(categories[:3])
                    response_text = f"We might not have that specific item, but we have a great selection in categories like {category_list}. Would you like to know more about any of these?"
                else:
                    response_text = "I couldn't find any categories at the moment."
                
                yield Event(
                    author=self.name,
                    content=Content(parts=[Part.from_text(text=response_text)]),
                    actions=EventActions(turn_complete=True) # End the turn here
                )
                return # Exit after yielding category response
            elif "item_code" in columns and not products_to_recommend: # No products found from a product query (fallback if products_data was not set)
                yield Event(
                    author=self.name,
                    content=Content(parts=[Part.from_text(text="I'm sorry, it looks like we're currently out of that.")]),
                    actions=EventActions(turn_complete=True) # End the turn here
                )
                return # Exit after yielding no-product response
        
        elif database_output and database_output.get("status") == "error":
            # Error from database tool, LLM should handle conversationally
            yield Event(
                author=self.name,
                content=Content(parts=[Part.from_text(text="Apologies, my mind seems to be drawing a blank on that one at the moment. Could you ask in a different way?")]),
                actions=EventActions(turn_complete=True) # End the turn here
            )

recommendation_agent = RecommendationAgent(
    name="recommendation_agent",
    description="Agent to format database results into a recommendation.",
)
