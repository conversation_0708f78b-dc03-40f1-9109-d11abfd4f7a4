import requests
import json
from typing import List

def get_product_info(upcs: List[str]) -> dict:
    """
    Calls an external API to get detailed product information for a list of UPCs.
    """
    print(f"[product_info_tool]: Getting product info for UPCs: {upcs}")
    url = "https://backend-upc-pull-dev.jmscpos.online/api/ProductSearch/BatchSearch"
    headers = {
        "accept": "*/*",
        "Content-Type": "application/json"
    }
    try:
        response = requests.post(url, headers=headers, data=json.dumps(upcs))
        response.raise_for_status()  # Raise an exception for bad status codes
        return {"status": "success", "data": response.json()}
    except requests.exceptions.RequestException as e:
        return {"status": "error", "message": str(e)}
