version: "3.8"

services:
  backend:
    build: ./backend # Ensure ./backend contains a Dockerfile
    ports:
      - "8000:8084"
    volumes:
      - ./backend/app:/app
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - MYSQL_HOST=db
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DB=${MYSQL_DB}
      - DB_VIEW_NAME=${DB_VIEW_NAME}

    depends_on:
      - db
    healthcheck: # Add healthcheck for backend
      test: ["CMD", "curl", "-f", "http://localhost:8084/admin_api/config"] # Check a simple endpoint
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s # Give the app some time to start up

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    volumes:
      - ./frontend/public:/usr/share/nginx/html
    depends_on:
      backend: # Ensure frontend waits for backend to be healthy
        condition: service_healthy

  db:
    image: mysql:8.0 # Using MySQL 8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD} # Root password for initial setup
      MYSQL_DATABASE: ${MYSQL_DB}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3308:3306" # Expose MySQL port
    volumes:
      - db_data:/var/lib/mysql
      - ./backend/01_init_tables.sql:/docker-entrypoint-initdb.d/01_init_tables.sql
      - ./backend/02_init_view.sql:/docker-entrypoint-initdb.d/02_init_view.sql

  # ngrok:
  #   image: ngrok/ngrok:latest
  #   environment:
  #     - NGROK_AUTHTOKEN=${NGROK_AUTHTOKEN}
  #   command: ["http", "--url=game-morally-lab.ngrok-free.app", "frontend:80"]
  #   restart: unless-stopped
  #   depends_on:
  #     - frontend

volumes:
  db_data: # Define the named volume for database persistence
